package main

import (
	"bytes"
	"context"
	"encoding/binary"
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"strconv"
	"syscall"
	"time"

	"github.com/cilium/ebpf/link"
	"github.com/cilium/ebpf/ringbuf"
	"github.com/cilium/ebpf/rlimit"
	"golang.org/x/sys/unix"
)

//go:generate go run github.com/cilium/ebpf/cmd/bpf2go -cc clang -cflags "-O2 -g -Wall -Werror" -no-global-types schedlat bpf/schedlat.c

type SchedEvent struct {
	Pid        uint32
	Tgid       uint32
	Comm       [16]byte
	WakeupTime uint64
	SwitchTime uint64
	LatencyNs  uint64
}

// 全局偏移量：CLOCK_MONOTONIC → CLOCK_REALTIME
var monoToRealOffset time.Duration

func init() {
	var ts unix.Timespec
	if err := unix.ClockGettime(unix.CLOCK_MONOTONIC, &ts); err != nil {
		log.Fatalf("clock_gettime(CLOCK_MONOTONIC) failed: %v", err)
	}
	mono := time.Unix(0, ts.Nano())
	now := time.Now()
	monoToRealOffset = now.Sub(mono)
}

// 将内核 monotonic 时间戳转换为本地时间
func convertKernelTimeToLocal(kernelNs uint64) time.Time {
	monoTime := time.Unix(0, int64(kernelNs)) // event.SwitchTime 是 monotonic ns
	return monoTime.Add(monoToRealOffset)
}

// 获取指定进程的所有线程PID
func getProcessThreads(pid int) ([]int, error) {
	taskDir := fmt.Sprintf("/proc/%d/task", pid)

	// 检查进程是否存在
	if _, err := os.Stat(taskDir); os.IsNotExist(err) {
		return nil, fmt.Errorf("进程 %d 不存在", pid)
	}

	entries, err := os.ReadDir(taskDir)
	if err != nil {
		return nil, fmt.Errorf("读取 %s 失败: %v", taskDir, err)
	}

	var pids []int
	for _, entry := range entries {
		if entry.IsDir() {
			if tid, err := strconv.Atoi(entry.Name()); err == nil {
				pids = append(pids, tid)
			}
		}
	}

	return pids, nil
}

// 设置PID过滤器
func setupPidFilter(objs *schedlatObjects, targetPid int) error {
	if targetPid <= 0 {
		return nil // 不设置过滤器，监控所有进程
	}

	// 获取目标进程的所有线程
	pids, err := getProcessThreads(targetPid)
	if err != nil {
		return err
	}

	// 设置过滤器启用标志，key为0, value为1表示启用
	zero := uint32(0)
	one := uint32(1)
	if err := objs.PidFilter.Put(&zero, &one); err != nil {
		return fmt.Errorf("设置过滤器启用标志失败: %v", err)
	}

	// 将所有线程PID添加到过滤器，key为pid, value为1表示需要监控
	for _, pid := range pids {
		pidKey := uint32(pid)
		if err := objs.PidFilter.Put(&pidKey, &one); err != nil {
			return fmt.Errorf("添加PID %d 到过滤器失败: %v", pid, err)
		}
	}

	return nil
}

func main() {
	// 解析命令行参数
	var targetPid = flag.Int("p", 0, "指定要监控的进程PID(默认监控所有进程)")
	flag.Parse()

	// 移除内存限制
	if err := rlimit.RemoveMemlock(); err != nil {
		log.Fatal(err)
	}

	// 加载 BPF 对象
	spec, err := loadSchedlat()
	if err != nil {
		log.Fatal(err)
	}

	objs := schedlatObjects{}
	if err := spec.LoadAndAssign(&objs, nil); err != nil {
		log.Fatal(err)
	}
	defer objs.Close()

	// 设置PID过滤器
	if err := setupPidFilter(&objs, *targetPid); err != nil {
		log.Fatal(err)
	}

	// 附加 tracepoints
	wakeupLink, err := link.Tracepoint("sched", "sched_wakeup", objs.HandleSchedWakeup, nil)
	if err != nil {
		log.Fatal(err)
	}
	defer wakeupLink.Close()

	switchLink, err := link.Tracepoint("sched", "sched_switch", objs.HandleSchedSwitch, nil)
	if err != nil {
		log.Fatal(err)
	}
	defer switchLink.Close()

	fmt.Println("监控调度延迟... 按 Ctrl+C 退出")
	fmt.Printf("%-29s %-8s %-16s %-12s\n", "TIMESTAMP", "PID", "COMM", "LATENCY(us)")

	// 打开 ringbuf reader
	rd, err := ringbuf.NewReader(objs.Events)
	if err != nil {
		log.Fatal(err)
	}
	defer rd.Close()

	// 设置信号处理
	ctx, cancel := context.WithCancel(context.Background())
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)
	go func() {
		<-c
		cancel()
	}()

	// 读取事件
	go func() {
		for {
			record, err := rd.Read()
			if err != nil {
				if ctx.Err() != nil {
					return
				}
				log.Printf("读取 ringbuf 失败: %v", err)
				continue
			}

			var event SchedEvent
			if err := binary.Read(bytes.NewBuffer(record.RawSample), binary.LittleEndian, &event); err != nil {
				log.Printf("解析事件失败: %v", err)
				continue
			}

			comm := string(event.Comm[:bytes.IndexByte(event.Comm[:], 0)])
			latencyUs := float64(event.LatencyNs) / 1000.0

			// 转换时间戳
			eventTime := convertKernelTimeToLocal(event.SwitchTime)
			timeStr := eventTime.Format("2006-01-02 15:04:05.000000000")

			fmt.Printf("%-29s %-8d %-16s %-12.3f\n",
				timeStr, event.Pid, comm, latencyUs)
		}
	}()

	<-ctx.Done()
	fmt.Println("\n程序退出")
}
