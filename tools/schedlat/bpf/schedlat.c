#include "schedlat.h"
#include <bpf/bpf_helpers.h>
#include <bpf/bpf_tracing.h>


struct {
    __uint(type, BPF_MAP_TYPE_RINGBUF);
    __uint(max_entries, 256 * 1024);
} events SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, 10240);
    __type(key, u32);
    __type(value, u64);
} wakeup_times SEC(".maps");

// PID过滤map，key是PID，value是1表示需要监控
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, 1024);
    __type(key, u32);
    __type(value, u32);
} pid_filter SEC(".maps");

SEC("tracepoint/sched/sched_wakeup")
int handle_sched_wakeup(struct trace_event_raw_sched_wakeup_template *ctx)
{
    //直接通过结构体获取成员
    u32 pid = ctx->pid;
    u64 ts = bpf_ktime_get_ns();

    //过滤掉idle进程
    if(pid == 0){
        return 0;
    }
    
    // 检查PID过滤器，如果过滤器不为空且PID不在过滤列表中，则跳过
    if (bpf_map_lookup_elem(&pid_filter, &pid) == NULL) {
        // 检查过滤器是否为空（通过检查一个特殊的key=0）
        u32 zero = 0;
        u32 *filter_enabled = bpf_map_lookup_elem(&pid_filter, &zero);
        if (filter_enabled != NULL) {
            // 过滤器已启用但PID不在列表中，跳过
            return 0;
        }
    }

    bpf_map_update_elem(&wakeup_times, &pid, &ts, BPF_ANY);

    return 0;
}

SEC("tracepoint/sched/sched_switch")
int handle_sched_switch(struct trace_event_raw_sched_switch *ctx)
{
    u32 next_pid = ctx->next_pid;
    u64 now = bpf_ktime_get_ns();

    // 检查PID过滤器，如果过滤器不为空且PID不在过滤列表中，则跳过
    if (bpf_map_lookup_elem(&pid_filter, &next_pid) == NULL) {
        // 检查过滤器是否为空（通过检查一个特殊的key=0）
        u32 zero = 0;
        u32 *filter_enabled = bpf_map_lookup_elem(&pid_filter, &zero);
        if (filter_enabled != NULL) {
            // 过滤器已启用但PID不在列表中，跳过
            return 0;
        }
    }

    // 查找被调度进程的上次唤醒时间
    u64 *wakeup_ts = bpf_map_lookup_elem(&wakeup_times, &next_pid);
    if (!wakeup_ts) {
        return 0;
    }
    
    u64 latency = now - *wakeup_ts;
    
    // 过滤掉太小的延迟
    if (latency <= 10000000) { // 小于10ms
        bpf_map_delete_elem(&wakeup_times, &next_pid);
        return 0;
    }
    
    struct sched_event *e;
    e = bpf_ringbuf_reserve(&events, sizeof(*e), 0);
    if (!e) {
        return 0;
    }
    
    e->pid = next_pid;
    e->tgid = next_pid;

    // 使用 __builtin_memcpy 进行高效复制
    __builtin_memcpy(e->comm, ctx->next_comm, sizeof(e->comm));
    e->wakeup_time = *wakeup_ts;
    e->switch_time = now;
    e->latency_ns = latency;
    
    //bpf_printk("submit: %d, %s, %lld, %lld, %lld\n", e->pid, e->comm, e->wakeup_time, e->switch_time, e->latency_ns);

    bpf_ringbuf_submit(e, 0);
    
    // 清理映射条目
    bpf_map_delete_elem(&wakeup_times, &next_pid);
    
    return 0;
}

char LICENSE[] SEC("license") = "GPL";