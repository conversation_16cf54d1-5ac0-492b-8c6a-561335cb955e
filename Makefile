GO      ?= go
CLANG   ?= clang
BPF_SRC = tools/schedlat/bpf/schedlat.c
BPF_OBJ = tools/schedlat/bpf/schedlat.o
OUTPUT_DIR = output
OUTPUT_BIN = $(OUTPUT_DIR)/schedlat

all: $(OUTPUT_BIN)

$(BPF_OBJ): $(BPF_SRC)
	$(CLANG) -O2 -g -target bpf -D__TARGET_ARCH_aarch64 -c $< -o $@

$(OUTPUT_BIN): $(BPF_OBJ) tools/schedlat/main.go go.mod | $(OUTPUT_DIR)
	cd tools/schedlat && $(GO) build -o ../../$@

$(OUTPUT_DIR):
	mkdir -p $(OUTPUT_DIR)

clean:
	rm -f $(OUTPUT_BIN) $(BPF_OBJ)
	rmdir $(OUTPUT_DIR) 2>/dev/null || true

.PHONY: all clean
