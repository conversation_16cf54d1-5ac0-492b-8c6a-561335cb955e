// Code generated by bpf2go; DO NOT EDIT.
//go:build mips || mips64 || ppc64 || s390x

package main

import (
	"bytes"
	_ "embed"
	"fmt"
	"io"

	"github.com/cilium/ebpf"
)

// loadSchedlat returns the embedded CollectionSpec for schedlat.
func loadSchedlat() (*ebpf.CollectionSpec, error) {
	reader := bytes.NewReader(_SchedlatBytes)
	spec, err := ebpf.LoadCollectionSpecFromReader(reader)
	if err != nil {
		return nil, fmt.Errorf("can't load schedlat: %w", err)
	}

	return spec, err
}

// loadSchedlatObjects loads schedlat and converts it into a struct.
//
// The following types are suitable as obj argument:
//
//	*schedlatObjects
//	*schedlatPrograms
//	*schedlatMaps
//
// See ebpf.CollectionSpec.LoadAndAssign documentation for details.
func loadSchedlatObjects(obj interface{}, opts *ebpf.CollectionOptions) error {
	spec, err := loadSchedlat()
	if err != nil {
		return err
	}

	return spec.LoadAndAssign(obj, opts)
}

// schedlatSpecs contains maps and programs before they are loaded into the kernel.
//
// It can be passed ebpf.CollectionSpec.Assign.
type schedlatSpecs struct {
	schedlatProgramSpecs
	schedlatMapSpecs
	schedlatVariableSpecs
}

// schedlatProgramSpecs contains programs before they are loaded into the kernel.
//
// It can be passed ebpf.CollectionSpec.Assign.
type schedlatProgramSpecs struct {
	HandleSchedSwitch *ebpf.ProgramSpec `ebpf:"handle_sched_switch"`
	HandleSchedWakeup *ebpf.ProgramSpec `ebpf:"handle_sched_wakeup"`
}

// schedlatMapSpecs contains maps before they are loaded into the kernel.
//
// It can be passed ebpf.CollectionSpec.Assign.
type schedlatMapSpecs struct {
	Events      *ebpf.MapSpec `ebpf:"events"`
	PidFilter   *ebpf.MapSpec `ebpf:"pid_filter"`
	WakeupTimes *ebpf.MapSpec `ebpf:"wakeup_times"`
}

// schedlatVariableSpecs contains global variables before they are loaded into the kernel.
//
// It can be passed ebpf.CollectionSpec.Assign.
type schedlatVariableSpecs struct {
}

// schedlatObjects contains all objects after they have been loaded into the kernel.
//
// It can be passed to loadSchedlatObjects or ebpf.CollectionSpec.LoadAndAssign.
type schedlatObjects struct {
	schedlatPrograms
	schedlatMaps
	schedlatVariables
}

func (o *schedlatObjects) Close() error {
	return _SchedlatClose(
		&o.schedlatPrograms,
		&o.schedlatMaps,
	)
}

// schedlatMaps contains all maps after they have been loaded into the kernel.
//
// It can be passed to loadSchedlatObjects or ebpf.CollectionSpec.LoadAndAssign.
type schedlatMaps struct {
	Events      *ebpf.Map `ebpf:"events"`
	PidFilter   *ebpf.Map `ebpf:"pid_filter"`
	WakeupTimes *ebpf.Map `ebpf:"wakeup_times"`
}

func (m *schedlatMaps) Close() error {
	return _SchedlatClose(
		m.Events,
		m.PidFilter,
		m.WakeupTimes,
	)
}

// schedlatVariables contains all global variables after they have been loaded into the kernel.
//
// It can be passed to loadSchedlatObjects or ebpf.CollectionSpec.LoadAndAssign.
type schedlatVariables struct {
}

// schedlatPrograms contains all programs after they have been loaded into the kernel.
//
// It can be passed to loadSchedlatObjects or ebpf.CollectionSpec.LoadAndAssign.
type schedlatPrograms struct {
	HandleSchedSwitch *ebpf.Program `ebpf:"handle_sched_switch"`
	HandleSchedWakeup *ebpf.Program `ebpf:"handle_sched_wakeup"`
}

func (p *schedlatPrograms) Close() error {
	return _SchedlatClose(
		p.HandleSchedSwitch,
		p.HandleSchedWakeup,
	)
}

func _SchedlatClose(closers ...io.Closer) error {
	for _, closer := range closers {
		if err := closer.Close(); err != nil {
			return err
		}
	}
	return nil
}

// Do not access this directly.
//
//go:embed schedlat_bpfeb.o
var _SchedlatBytes []byte
